import { router, useForm, usePage } from '@inertiajs/react';
import { Check, ChevronsUpDown, Plus } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useInitials } from '@/hooks/use-initials';
import { type SharedData } from '@/types';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';

type OrganizationForm = {
    name: string;
};

export default function OrganizationSwitcher() {
    const getInitials = useInitials();
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

    const { auth } = usePage<SharedData>().props;

    const { data, setData, post, processing, errors, reset } = useForm<OrganizationForm>({
        name: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('organizations.store'), {
            onSuccess: () => {
                setIsCreateDialogOpen(false);
                reset();
            },
        });
    };

    const handleCreateClick = () => {
        setIsCreateDialogOpen(true);
    };

    const handleDialogClose = () => {
        setIsCreateDialogOpen(false);
        reset();
    };

    if (!auth.user || auth.organizations.length === 0) {
        return null;
    }

    const switchOrganization = (organizationSlug: string) => {
        router.post(route('organizations.switch', organizationSlug));
    };

    const currentOrganization = auth.current_organization;

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button size="icon" variant="ghost" className="group-data-[collapsible=icon]:hidden">
                        <ChevronsUpDown className="size-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="start">
                    {auth.organizations.map((organization) => (
                        <DropdownMenuItem
                            key={organization.id}
                            onClick={() => switchOrganization(organization.slug)}
                            className="flex items-center justify-between"
                        >
                            <div className="flex items-center gap-2 truncate">
                                <Avatar className="size-6 overflow-hidden rounded">
                                    <AvatarImage src={organization.logo_path ?? undefined} alt={organization.name} />
                                    <AvatarFallback className="flex w-full items-center justify-center rounded bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white">
                                        {getInitials(organization.name)}
                                    </AvatarFallback>
                                </Avatar>
                                <span className="text-sm font-medium truncate">{organization.name}</span>
                            </div>
                            {currentOrganization?.id === organization.id && <Check className="size-4" />}
                        </DropdownMenuItem>
                    ))}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleCreateClick}>
                        <Plus className="mr-2 size-4" />
                        Tạo tổ chức mới
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            <Dialog open={isCreateDialogOpen} onOpenChange={handleDialogClose}>
                <DialogContent className="sm:max-w-[425px]">
                    <form onSubmit={submit}>
                        <DialogHeader>
                            <DialogTitle>Tạo tổ chức mới</DialogTitle>
                            <DialogDescription>Tạo một tổ chức mới để cộng tác với nhóm của bạn.</DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">Tên tổ chức</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="Nhập tên tổ chức"
                                    required
                                    autoFocus
                                />
                                <InputError message={errors.name} />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={handleDialogClose}>
                                Hủy
                            </Button>
                            <Button type="submit" disabled={processing}>
                                {processing ? 'Đang tạo...' : 'Tạo tổ chức'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </>
    );
}
