import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { useInitials } from '@/hooks/use-initials';
import { SharedData, type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { BookOpen, Folder, LayoutGrid } from 'lucide-react';
import OrganizationSwitcher from './organization-switcher';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';

const mainNavItems: NavItem[] = [
    {
        title: 'Trang chủ',
        href: '/dashboard',
        icon: LayoutGrid,
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Kho',
        href: 'https://github.com/laravel/react-starter-kit',
        icon: Folder,
    },
    {
        title: 'Tài liệu',
        href: 'https://laravel.com/docs/starter-kits#react',
        icon: BookOpen,
    },
];

export function AppSidebar() {
    const page = usePage<SharedData>();
    const { auth } = page.props;
    const getInitials = useInitials();
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem className="flex items-center justify-between">
                        <SidebarMenuButton asChild size="lg" tooltip={{ children: auth.current_organization?.name }}>
                            <Link href={route('dashboard')} prefetch className="group-data-[collapsible=icon]:ps-0!">
                                <Avatar className="size-8 overflow-hidden rounded-sm">
                                    <AvatarImage src={auth.current_organization?.logo_path ?? undefined} alt={auth.current_organization?.name} />
                                    <AvatarFallback className="flex w-full items-center justify-center rounded bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white">
                                        {getInitials(auth.current_organization?.name ?? '')}
                                    </AvatarFallback>
                                </Avatar>
                                <span className="truncate font-medium">{auth.current_organization?.name}</span>
                            </Link>
                        </SidebarMenuButton>
                        <OrganizationSwitcher />
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
