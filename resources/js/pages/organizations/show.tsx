import { Head, Link, router, useForm, usePage } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { Edit, Mail, MoreHorizontal, Plus, Settings, Trash2, UserMinus, Users } from 'lucide-react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type Organization, type OrganizationInvitation, type OrganizationMember } from '@/types';

interface OrganizationShowProps {
    organization: Organization & {
        members: OrganizationMember[];
        pending_invitations: OrganizationInvitation[];
    };
    [key: string]: unknown;
}

type InvitationForm = {
    email: string;
    role: string;
};

export default function ShowOrganization() {
    const { organization } = usePage<OrganizationShowProps>().props;
    const [inviteDialogOpen, setInviteDialogOpen] = useState(false);

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Tổ chức',
            href: '/organizations',
        },
        {
            title: organization.name,
            href: `/organizations/${organization.slug}`,
        },
    ];

    const { data, setData, post, processing, errors, reset } = useForm<InvitationForm>({
        email: '',
        role: 'member',
    });

    const submitInvitation: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('organizations.invitations.store', organization.slug), {
            onSuccess: () => {
                reset();
                setInviteDialogOpen(false);
            },
        });
    };

    const removeMember = (memberId: number) => {
        if (confirm('Bạn có chắc chắn muốn xóa thành viên này?')) {
            router.delete(route('organizations.members.remove', [organization.slug, memberId]));
        }
    };

    const updateMemberRole = (memberId: number, role: string) => {
        router.patch(route('organizations.members.update-role', [organization.slug, memberId]), {
            role,
        });
    };

    const cancelInvitation = (invitationId: number) => {
        if (confirm('Bạn có chắc chắn muốn hủy lời mời này?')) {
            router.delete(route('organizations.invitations.destroy', [organization.slug, invitationId]));
        }
    };

    const resendInvitation = (invitationId: number) => {
        router.post(route('organizations.invitations.resend', [organization.slug, invitationId]));
    };

    const getRoleBadge = (role: string) => {
        const roleColors = {
            owner: 'bg-primary text-primary-foreground',
            admin: 'bg-secondary text-secondary-foreground',
            member: 'bg-muted text-muted-foreground',
        };

        const roleLabels = {
            owner: 'Chủ sở hữu',
            admin: 'Quản trị viên',
            member: 'Thành viên',
        };

        return (
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${roleColors[role as keyof typeof roleColors]}`}>
                {roleLabels[role as keyof typeof roleLabels]}
            </span>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={organization.name} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-semibold">{organization.name}</h1>
                        {organization.description && (
                            <p className="text-muted-foreground">{organization.description}</p>
                        )}
                    </div>
                    <div className="flex gap-2">
                        {organization.is_admin && (
                            <>
                                <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
                                    <DialogTrigger asChild>
                                        <Button>
                                            <Plus className="mr-2 h-4 w-4" />
                                            Mời thành viên
                                        </Button>
                                    </DialogTrigger>
                                    <DialogContent>
                                        <DialogHeader>
                                            <DialogTitle>Mời thành viên mới</DialogTitle>
                                            <DialogDescription>
                                                Gửi lời mời qua email để thêm thành viên vào tổ chức.
                                            </DialogDescription>
                                        </DialogHeader>
                                        <form onSubmit={submitInvitation}>
                                            <div className="space-y-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="email">Email</Label>
                                                    <Input
                                                        id="email"
                                                        type="email"
                                                        value={data.email}
                                                        onChange={(e) => setData('email', e.target.value)}
                                                        placeholder="<EMAIL>"
                                                        required
                                                    />
                                                    <InputError message={errors.email} />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="role">Vai trò</Label>
                                                    <Select value={data.role} onValueChange={(value) => setData('role', value)}>
                                                        <SelectTrigger>
                                                            <SelectValue />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="member">Thành viên</SelectItem>
                                                            <SelectItem value="admin">Quản trị viên</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <InputError message={errors.role} />
                                                </div>
                                            </div>
                                            <DialogFooter className="mt-6">
                                                <Button type="button" variant="outline" onClick={() => setInviteDialogOpen(false)}>
                                                    Hủy
                                                </Button>
                                                <Button type="submit" disabled={processing}>
                                                    {processing ? 'Đang gửi...' : 'Gửi lời mời'}
                                                </Button>
                                            </DialogFooter>
                                        </form>
                                    </DialogContent>
                                </Dialog>
                                <Button variant="outline" asChild>
                                    <Link href={route('organizations.edit', organization.slug)}>
                                        <Edit className="mr-2 h-4 w-4" />
                                        Chỉnh sửa
                                    </Link>
                                </Button>
                            </>
                        )}
                    </div>
                </div>

                {/* Stats */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Tổng thành viên</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{organization.members.length}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Lời mời đang chờ</CardTitle>
                            <Mail className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{organization.pending_invitations.length}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Chủ sở hữu</CardTitle>
                            <Settings className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-sm font-medium">{organization.owner.name}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Members Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Thành viên</CardTitle>
                        <CardDescription>Quản lý thành viên trong tổ chức</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-2">Tên</th>
                                        <th className="text-left p-2">Email</th>
                                        <th className="text-left p-2">Vai trò</th>
                                        <th className="text-left p-2">Ngày tham gia</th>
                                        <th className="text-right p-2">Hành động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {organization.members.map((member) => (
                                        <tr key={member.id} className="border-b">
                                            <td className="p-2 font-medium">{member.name}</td>
                                            <td className="p-2">{member.email}</td>
                                            <td className="p-2">{getRoleBadge(member.role)}</td>
                                            <td className="p-2">{new Date(member.joined_at).toLocaleDateString('vi-VN')}</td>
                                            <td className="p-2 text-right">
                                                {organization.is_admin && member.role !== 'owner' && (
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" size="sm">
                                                                <MoreHorizontal className="h-4 w-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuItem
                                                                onClick={() => updateMemberRole(member.id, member.role === 'admin' ? 'member' : 'admin')}
                                                            >
                                                                {member.role === 'admin' ? 'Hạ xuống thành viên' : 'Thăng lên quản trị viên'}
                                                            </DropdownMenuItem>
                                                            <DropdownMenuSeparator />
                                                            <DropdownMenuItem
                                                                onClick={() => removeMember(member.id)}
                                                                className="text-destructive"
                                                            >
                                                                <UserMinus className="mr-2 h-4 w-4" />
                                                                Xóa khỏi tổ chức
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                )}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>

                {/* Pending Invitations */}
                {organization.pending_invitations.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Lời mời đang chờ</CardTitle>
                            <CardDescription>Các lời mời chưa được chấp nhận</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b">
                                            <th className="text-left p-2">Email</th>
                                            <th className="text-left p-2">Vai trò</th>
                                            <th className="text-left p-2">Người mời</th>
                                            <th className="text-left p-2">Ngày gửi</th>
                                            <th className="text-right p-2">Hành động</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {organization.pending_invitations.map((invitation) => (
                                            <tr key={invitation.id} className="border-b">
                                                <td className="p-2 font-medium">{invitation.email}</td>
                                                <td className="p-2">{getRoleBadge(invitation.role)}</td>
                                                <td className="p-2">{invitation.invited_by.name}</td>
                                                <td className="p-2">{new Date(invitation.created_at).toLocaleDateString('vi-VN')}</td>
                                                <td className="p-2 text-right">
                                                    {organization.is_admin && (
                                                        <div className="flex gap-2 justify-end">
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => resendInvitation(invitation.id)}
                                                            >
                                                                Gửi lại
                                                            </Button>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => cancelInvitation(invitation.id)}
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    )}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
} 