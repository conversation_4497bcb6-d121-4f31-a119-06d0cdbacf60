import { Head, Link, router, usePage } from '@inertiajs/react';
import { Building2, Calendar, Mail, User } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AuthCardLayout from '@/layouts/auth/auth-card-layout';

interface InvitationProps {
    invitation: {
        id: number;
        email: string;
        role: string;
        expires_at: string;
        organization: {
            id: number;
            name: string;
            description?: string;
        };
        invited_by: {
            name: string;
            email: string;
        };
    };
    [key: string]: unknown;
}

export default function OrganizationInvitation() {
    const { invitation } = usePage<InvitationProps>().props;

    const acceptInvitation = () => {
        router.post(route('invitations.accept', invitation.id));
    };

    const declineInvitation = () => {
        if (confirm('Bạn có chắc chắn muốn từ chối lời mời này?')) {
            router.post(route('invitations.decline', invitation.id));
        }
    };

    const getRoleLabel = (role: string) => {
        const roleLabels = {
            owner: 'Ch<PERSON> sở hữu',
            admin: 'Quản trị viên',
            member: 'Thành viên',
        };
        return roleLabels[role as keyof typeof roleLabels] || role;
    };

    const isExpired = new Date(invitation.expires_at) < new Date();

    return (
        <AuthCardLayout
            title="Lời mời tham gia tổ chức"
            description="Bạn đã được mời tham gia một tổ chức"
        >
            <Head title="Lời mời tổ chức" />

            <div className="space-y-6">
                {/* Organization Info */}
                <Card>
                    <CardHeader className="text-center">
                        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                            <Building2 className="h-6 w-6 text-primary" />
                        </div>
                        <CardTitle className="text-xl">{invitation.organization.name}</CardTitle>
                        {invitation.organization.description && (
                            <CardDescription>{invitation.organization.description}</CardDescription>
                        )}
                    </CardHeader>
                </Card>

                {/* Invitation Details */}
                <div className="space-y-4">
                    <div className="flex items-center gap-3 text-sm">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Được mời với vai trò:</span>
                        <span className="font-medium">{getRoleLabel(invitation.role)}</span>
                    </div>

                    <div className="flex items-center gap-3 text-sm">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Người mời:</span>
                        <span className="font-medium">{invitation.invited_by.name}</span>
                    </div>

                    <div className="flex items-center gap-3 text-sm">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Hết hạn:</span>
                        <span className={`font-medium ${isExpired ? 'text-destructive' : ''}`}>
                            {new Date(invitation.expires_at).toLocaleDateString('vi-VN', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit',
                            })}
                        </span>
                    </div>
                </div>

                {/* Actions */}
                {isExpired ? (
                    <div className="text-center">
                        <p className="text-destructive text-sm mb-4">Lời mời này đã hết hạn.</p>
                        <Button variant="outline" asChild>
                            <Link href={route('login')}>Quay lại đăng nhập</Link>
                        </Button>
                    </div>
                ) : (
                    <div className="flex gap-3">
                        <Button onClick={acceptInvitation} className="flex-1">
                            Chấp nhận lời mời
                        </Button>
                        <Button onClick={declineInvitation} variant="outline" className="flex-1">
                            Từ chối
                        </Button>
                    </div>
                )}

                <div className="text-center">
                    <p className="text-xs text-muted-foreground">
                        Bằng cách chấp nhận lời mời, bạn đồng ý tham gia tổ chức này với vai trò được chỉ định.
                    </p>
                </div>
            </div>
        </AuthCardLayout>
    );
} 