import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type Organization } from '@/types';

interface OrganizationEditProps {
    organization: Organization;
    [key: string]: unknown;
}

type OrganizationForm = {
    name: string;
    slug: string;
    description: string;
};

export default function EditOrganization() {
    const { organization } = usePage<OrganizationEditProps>().props;

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Tổ chức',
            href: '/organizations',
        },
        {
            title: organization.name,
            href: `/organizations/${organization.slug}`,
        },
        {
            title: 'Chỉnh sửa',
            href: `/organizations/${organization.slug}/edit`,
        },
    ];

    const { data, setData, patch, processing, errors } = useForm<OrganizationForm>({
        name: organization.name,
        slug: organization.slug,
        description: organization.description || '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        patch(route('organizations.update', organization.slug));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Chỉnh sửa ${organization.name}`} />

            <div className="max-w-2xl">
                <div className="mb-6">
                    <h1 className="text-2xl font-semibold">Chỉnh sửa tổ chức</h1>
                    <p className="text-muted-foreground">
                        Cập nhật thông tin tổ chức của bạn.
                    </p>
                </div>

                <form onSubmit={submit} className="space-y-6">
                    <div className="space-y-2">
                        <Label htmlFor="name">Tên tổ chức</Label>
                        <Input
                            id="name"
                            type="text"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            placeholder="Nhập tên tổ chức"
                            required
                            autoFocus
                        />
                        <InputError message={errors.name} />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="slug">Slug</Label>
                        <Input
                            id="slug"
                            type="text"
                            value={data.slug}
                            onChange={(e) => setData('slug', e.target.value)}
                            placeholder="organization-slug"
                            required
                        />
                        <p className="text-sm text-muted-foreground">
                            URL của tổ chức sẽ là: {window.location.origin}/organizations/{data.slug}
                        </p>
                        <InputError message={errors.slug} />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="description">Mô tả (tùy chọn)</Label>
                        <textarea
                            id="description"
                            value={data.description}
                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setData('description', e.target.value)}
                            placeholder="Mô tả ngắn về tổ chức của bạn"
                            rows={3}
                            className="border-input placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex min-h-[60px] w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive"
                        />
                        <InputError message={errors.description} />
                    </div>

                    <div className="flex gap-4">
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Đang cập nhật...' : 'Cập nhật tổ chức'}
                        </Button>
                        <Button type="button" variant="outline" asChild>
                            <Link href={route('organizations.show', organization.slug)}>Hủy</Link>
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
} 