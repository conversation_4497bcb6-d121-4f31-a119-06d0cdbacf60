import { Head, Link, usePage } from '@inertiajs/react';
import { Building2, Plus, Users } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type SharedData } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Tổ chức',
        href: '/organizations',
    },
];

export default function OrganizationsIndex() {
    const { auth } = usePage<SharedData>().props;

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Tổ chức" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-semibold">Tổ chức</h1>
                        <p className="text-muted-foreground"><PERSON><PERSON><PERSON>n lý các tổ chức của bạn</p>
                    </div>
                    <Button asChild>
                        <Link href={route('organizations.create')}>
                            <Plus className="mr-2 h-4 w-4" />
                            Tạo tổ chức
                        </Link>
                    </Button>
                </div>

                {auth.organizations.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-medium mb-2">Chưa có tổ chức nào</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                Bạn chưa thuộc về tổ chức nào. Tạo tổ chức đầu tiên của bạn để bắt đầu.
                            </p>
                            <Button asChild>
                                <Link href={route('organizations.create')}>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Tạo tổ chức đầu tiên
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {auth.organizations.map((organization) => (
                            <Card key={organization.id} className="hover:shadow-md transition-shadow">
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-lg">{organization.name}</CardTitle>
                                        {organization.is_owner && (
                                            <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                                                Chủ sở hữu
                                            </span>
                                        )}
                                        {organization.is_admin && !organization.is_owner && (
                                            <span className="bg-secondary text-secondary-foreground text-xs px-2 py-1 rounded-full">
                                                Quản trị viên
                                            </span>
                                        )}
                                    </div>
                                    {organization.description && (
                                        <CardDescription>{organization.description}</CardDescription>
                                    )}
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <Users className="mr-1 h-4 w-4" />
                                            {organization.members_count} thành viên
                                        </div>
                                        <div className="flex gap-2">
                                            {auth.current_organization?.id !== organization.id && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    asChild
                                                >
                                                    <Link
                                                        href={route('organizations.switch', organization.slug)}
                                                        method="post"
                                                        as="button"
                                                    >
                                                        Chuyển đến
                                                    </Link>
                                                </Button>
                                            )}
                                            <Button variant="outline" size="sm" asChild>
                                                <Link href={route('organizations.show', organization.slug)}>
                                                    Xem chi tiết
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}
            </div>
        </AppLayout>
    );
} 