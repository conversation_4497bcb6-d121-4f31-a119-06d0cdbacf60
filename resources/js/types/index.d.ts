import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    current_organization_id?: number | null;
    created_at: string;
    updated_at: string;
    [key: string]: unknown;
}

export interface Organization {
    id: number;
    name: string;
    slug: string;
    description?: string | null;
    logo_path?: string | null;
    user_role: string;
    is_owner: boolean;
    is_admin: boolean;
    owner: {
        id: number;
        name: string;
        email: string;
    };
    members_count: number;
    created_at?: string;
    updated_at?: string;
}

export interface OrganizationMember {
    id: number;
    name: string;
    email: string;
    role: string;
    joined_at: string;
}

export interface OrganizationInvitation {
    id: number;
    email: string;
    role: string;
    invited_by: {
        name: string;
        email: string;
    };
    expires_at: string;
    created_at: string;
}

export interface Auth {
    user: User;
    current_organization: Organization | null;
    organizations: Organization[];
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    [key: string]: unknown;
}
