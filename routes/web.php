<?php

use App\Http\Controllers\{
    OrganizationController,
    OrganizationInvitationController
};
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    Route::post('organizations', [OrganizationController::class, 'store'])->name('organizations.store');
    Route::post('organizations/{organization}/switch', [OrganizationController::class, 'switch'])->name('organizations.switch');
    Route::patch('organizations/{organization}/members/{user}/role', [OrganizationController::class, 'updateMemberRole'])->name('organizations.members.update-role');
    Route::delete('organizations/{organization}/members/{user}', [OrganizationController::class, 'removeMember'])->name('organizations.members.remove');
    Route::post('organizations/{organization}/leave', [OrganizationController::class, 'leave'])->name('organizations.leave');

    Route::post('organizations/{organization}/invitations', [OrganizationInvitationController::class, 'store'])->name('organizations.invitations.store');
    Route::delete('organizations/{organization}/invitations/{invitation}', [OrganizationInvitationController::class, 'destroy'])->name('organizations.invitations.destroy');
    Route::post('organizations/{organization}/invitations/{invitation}/resend', [OrganizationInvitationController::class, 'resend'])->name('organizations.invitations.resend');
});

Route::get('invitations/{invitation}', [OrganizationInvitationController::class, 'show'])->name('invitations.show');
Route::post('invitations/{invitation}/accept', [OrganizationInvitationController::class, 'accept'])->name('invitations.accept')->middleware('auth');
Route::post('invitations/{invitation}/decline', [OrganizationInvitationController::class, 'decline'])->name('invitations.decline');

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
