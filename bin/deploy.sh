#!/bin/bash

set -e

TELEGRAM_BOT_TOKEN="**********************************************"
TELEGRAM_CHAT_ID="-4591076848"

send_telegram_message() {
    local message="$1"
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
        -d "chat_id=${TELEGRAM_CHAT_ID}" \
        -d "text=${message}" \
        -d "parse_mode=HTML"
}

BRANCH=$(git rev-parse --abbrev-ref HEAD)
COMMIT_HASH=$(git rev-parse --short HEAD)
COMMIT_MESSAGE=$(git log -1 --pretty=%B)
COMMIT_AUTHOR=$(git log -1 --pretty=%an)

send_telegram_message "🚀 <b>Deployment Started</b>

📦 Project: <b><PERSON><PERSON></b>
🌿 Branch: <code>${BRANCH}</code>
🔨 Commit: <code>${COMMIT_HASH}</code>
👤 Author: ${COMMIT_AUTHOR}
📝 Message: ${COMMIT_MESSAGE}"

echo "🚀 Starting deployment..."

echo "📥 Pulling latest changes..."
git config --global --add safe.directory /home/<USER>/laravel.ngoquocdat.dev
git pull origin main

echo "📦 Installing Composer dependencies..."
composer install --no-dev --optimize-autoloader

echo "📦 Installing Node dependencies..."
npm ci

echo "🏗️ Building frontend assets..."
npm run build:ssr

echo "🧹 Clearing and caching..."
php artisan optimize:clear
php artisan optimize

echo "🔄 Running database migrations..."
php artisan migrate --force

echo "🔄 Restarting services..."
sudo systemctl restart php8.4-fpm
sudo systemctl restart nginx

send_telegram_message "✅ <b>Deployment Completed Successfully!</b>

📦 Project: <b>Laravel</b>
🌿 Branch: <code>${BRANCH}</code>
🔨 Commit: <code>${COMMIT_HASH}</code>
⏱️ Time: $(date '+%Y-%m-%d %H:%M:%S')"

echo "✅ Deployment completed successfully!"

