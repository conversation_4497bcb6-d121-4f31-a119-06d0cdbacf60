<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Inspiring;
use Illuminate\Http\Request;
use Inertia\Middleware;
use Tighten\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    protected $rootView = 'app';

    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    public function share(Request $request): array
    {
        [$message, $author] = str(Inspiring::quotes()->random())->explode('-');

        $user = $request->user();

        return [
            ...parent::share($request),
            'name' => config('app.name'),
            'quote' => ['message' => trim($message), 'author' => trim($author)],
            'auth' => [
                'user' => $user ? [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'email_verified_at' => $user->email_verified_at,
                    'current_organization_id' => $user->current_organization_id,
                ] : null,
                'current_organization' => $user?->currentOrganization ? [
                    'id' => $user->currentOrganization->id,
                    'name' => $user->currentOrganization->name,
                    'slug' => $user->currentOrganization->slug,
                    'description' => $user->currentOrganization->description,
                    'logo_path' => $user->currentOrganization->logo_path,
                    'user_role' => $user->getOrganizationRole($user->currentOrganization),
                    'is_owner' => $user->ownsOrganization($user->currentOrganization),
                    'is_admin' => $user->isOrganizationAdmin($user->currentOrganization),
                ] : null,
                'organizations' => $user ? $user->organizations()->with('owner')->get()->map(function ($org) use ($user) {
                    return [
                        'id' => $org->id,
                        'name' => $org->name,
                        'slug' => $org->slug,
                        'description' => $org->description,
                        'logo_path' => $org->logo_path,
                        'user_role' => $user->getOrganizationRole($org),
                        'is_owner' => $user->ownsOrganization($org),
                        'is_admin' => $user->isOrganizationAdmin($org),
                        'owner' => [
                            'id' => $org->owner->id,
                            'name' => $org->owner->name,
                            'email' => $org->owner->email,
                        ],
                        'members_count' => $org->users()->count(),
                    ];
                }) : [],
            ],
            'ziggy' => fn (): array => [
                ...(new Ziggy)->toArray(),
                'location' => $request->url(),
            ],
            'sidebarOpen' => ! $request->hasCookie('sidebar_state') || $request->cookie('sidebar_state') === 'true',
        ];
    }
}
