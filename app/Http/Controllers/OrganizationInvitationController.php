<?php

namespace App\Http\Controllers;

use App\Enums\OrganizationRole;
use App\Models\Organization;
use App\Models\OrganizationInvitation;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class OrganizationInvitationController extends Controller
{
    use AuthorizesRequests;

    public function store(Request $request, Organization $organization): RedirectResponse
    {
        $this->authorize('invite', $organization);

        $validated = $request->validate([
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('organization_invitations')->where(function (Builder $query) use ($organization) {
                    return $query
                        ->where('organization_id', $organization->id)
                        ->whereNull('accepted_at')
                        ->where('expires_at', '>', now());
                }),
            ],
            'role' => ['required', Rule::enum(OrganizationRole::class)],
        ]);

        $existingUser = User::where('email', $validated['email'])->first();
        if ($existingUser && $organization->hasUser($existingUser)) {
            return back()->withErrors(['email' => 'Người dùng này đã là thành viên của tổ chức.']);
        }

        $invitation = $organization->invitations()->create([
            'email' => $validated['email'],
            'role' => $validated['role'],
            'invited_by' => $request->user()->id,
        ]);


        return back()->with('status', 'Lời mời đã được gửi thành công.');
    }

    public function show(OrganizationInvitation $invitation): Response|RedirectResponse
    {
        if ($invitation->isExpired()) {
            return redirect()->route('login')->withErrors(['error' => 'Lời mời đã hết hạn.']);
        }

        if ($invitation->isAccepted()) {
            return redirect()->route('login')->with('status', 'Lời mời đã được chấp nhận trước đó.');
        }

        return Inertia::render('organizations/invitation', [
            'invitation' => [
                'id' => $invitation->id,
                'email' => $invitation->email,
                'role' => $invitation->role,
                'expires_at' => $invitation->expires_at,
                'organization' => [
                    'id' => $invitation->organization->id,
                    'name' => $invitation->organization->name,
                    'description' => $invitation->organization->description,
                ],
                'invited_by' => [
                    'name' => $invitation->invitedBy->name,
                    'email' => $invitation->invitedBy->email,
                ],
            ],
        ]);
    }

    public function accept(Request $request, OrganizationInvitation $invitation): RedirectResponse
    {
        if ($invitation->isExpired()) {
            return back()->withErrors(['error' => 'Lời mời đã hết hạn.']);
        }

        if ($invitation->isAccepted()) {
            return back()->withErrors(['error' => 'Lời mời đã được chấp nhận trước đó.']);
        }

        $user = $request->user();

        if ($user->email !== $invitation->email) {
            return back()->withErrors(['error' => 'Email của bạn không khớp với lời mời.']);
        }

        if ($invitation->organization->hasUser($user)) {
            return back()->withErrors(['error' => 'Bạn đã là thành viên của tổ chức này.']);
        }

        DB::transaction(function () use ($invitation, $user) {
            $invitation->organization->addUser($user, $invitation->role);

            $invitation->accept();

            if (!$user->current_organization_id) {
                $user->update(['current_organization_id' => $invitation->organization->id]);
            }
        });

        return redirect()->route('organizations.show', $invitation->organization)
            ->with('status', 'Bạn đã tham gia tổ chức thành công.');
    }

    public function decline(OrganizationInvitation $invitation): RedirectResponse
    {
        $invitation->delete();

        return redirect()->route('dashboard')
            ->with('status', 'Bạn đã từ chối lời mời.');
    }

    public function destroy(Organization $organization, OrganizationInvitation $invitation): RedirectResponse
    {
        $this->authorize('invite', $organization);

        $invitation->delete();

        return back()->with('status', 'Lời mời đã được hủy.');
    }

    public function resend(Request $request, Organization $organization, OrganizationInvitation $invitation): RedirectResponse
    {
        $this->authorize('invite', $organization);

        if ($invitation->isAccepted()) {
            return back()->withErrors(['error' => 'Lời mời đã được chấp nhận.']);
        }

        $invitation->update(['expires_at' => now()->addDays(7)]);


        return back()->with('status', 'Lời mời đã được gửi lại.');
    }
}
