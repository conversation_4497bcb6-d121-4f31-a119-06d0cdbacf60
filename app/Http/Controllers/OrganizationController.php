<?php

namespace App\Http\Controllers;

use App\Enums\OrganizationRole;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class OrganizationController extends Controller
{
    use AuthorizesRequests;

    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:organizations,name'],
        ]);

        $organization = DB::transaction(function () use ($validated, $request) {
            $organization = $request->user()->ownedOrganizations()->create([
                'name' => $validated['name'],
            ]);

            $organization->addUser($request->user(), OrganizationRole::Owner);

            if (! $request->user()->current_organization_id) {
                $request->user()->update(['current_organization_id' => $organization->id]);
            }

            return $organization;
        });

        $request->user()->switchOrganization($organization);

        return redirect()->route('dashboard');
    }

    public function update(Request $request, Organization $organization): RedirectResponse
    {
        $this->authorize('update', $organization);

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'slug' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-z0-9-]+$/',
                Rule::unique('organizations')->ignore($organization->id),
            ],
        ]);

        $organization->update([
            'name' => $validated['name'],
        ]);

        return back()->with('status', 'Tổ chức đã được cập nhật thành công.');
    }

    public function destroy(Organization $organization): RedirectResponse
    {
        $this->authorize('delete', $organization);

        $organization->delete();

        return redirect()->route('organizations.index')
            ->with('status', 'Tổ chức đã được xóa thành công.');
    }

    public function switch(Request $request, Organization $organization): RedirectResponse
    {
        if (! $request->user()->belongsToOrganization($organization)) {
            abort(403, 'Bạn không có quyền truy cập tổ chức này.');
        }

        $request->user()->switchOrganization($organization);

        return back()->with('status', "Đã chuyển sang tổ chức: {$organization->name}");
    }

    public function updateMemberRole(Request $request, Organization $organization, User $user): RedirectResponse
    {
        $this->authorize('updateMember', $organization);

        $validated = $request->validate([
            'role' => ['required', Rule::enum(OrganizationRole::class)],
        ]);

        if ($organization->ownedBy($user) && $validated['role'] !== 'owner') {
            return back()->withErrors(['role' => 'Không thể thay đổi vai trò của chủ sở hữu tổ chức.']);
        }

        $organization->updateUserRole($user, $validated['role']);

        return back()->with('status', 'Vai trò thành viên đã được cập nhật thành công.');
    }

    public function removeMember(Organization $organization, User $user): RedirectResponse
    {
        $this->authorize('removeMember', $organization);

        if ($organization->ownedBy($user)) {
            return back()->withErrors(['error' => 'Không thể xóa chủ sở hữu tổ chức.']);
        }

        $organization->removeUser($user);

        if ($user->current_organization_id === $organization->id) {
            $nextOrganization = $user->organizations()->first();
            $user->update(['current_organization_id' => $nextOrganization?->id]);
        }

        return back()->with('status', 'Thành viên đã được xóa khỏi tổ chức.');
    }

    public function leave(Request $request, Organization $organization): RedirectResponse
    {
        if ($organization->ownedBy($request->user())) {
            return back()->withErrors(['error' => 'Chủ sở hữu không thể rời khỏi tổ chức. Vui lòng chuyển quyền sở hữu hoặc xóa tổ chức.']);
        }

        $organization->removeUser($request->user());

        if ($request->user()->current_organization_id === $organization->id) {
            $nextOrganization = $request->user()->organizations()->first();
            $request->user()->update(['current_organization_id' => $nextOrganization?->id]);
        }

        return redirect()
            ->route('organizations.index')
            ->with('status', "Bạn đã rời khỏi tổ chức: {$organization->name}");
    }
}
