<?php

namespace App\Http\Controllers;

use App\Models\Organization;
use App\Models\Plan;
use App\Models\Subscription;
use App\Services\SubscriptionUsageService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SubscriptionController extends Controller
{
    use AuthorizesRequests;
{
    public function __construct(
        private SubscriptionUsageService $usageService
    ) {}

    /**
     * Get available plans
     */
    public function plans(): JsonResponse
    {
        $plans = Plan::active()->ordered()->get();
        
        return response()->json([
            'data' => $plans
        ]);
    }

    /**
     * Get organization's subscription status
     */
    public function status(Organization $organization): JsonResponse
    {
        $this->authorize('view', $organization);
        
        $usageSummary = $this->usageService->getUsageSummary($organization);
        
        return response()->json([
            'data' => $usageSummary
        ]);
    }

    /**
     * Subscribe to a plan
     */
    public function subscribe(Request $request, Organization $organization): JsonResponse
    {
        $this->authorize('manage', $organization);
        
        $request->validate([
            'plan_id' => 'required|exists:plans,id',
            'payment_method' => 'required|in:stripe,bank_transfer',
            'billing_cycle' => 'sometimes|in:monthly,yearly'
        ]);

        $plan = Plan::findOrFail($request->plan_id);
        
        // Cancel existing active subscription
        if ($organization->hasActiveSubscription()) {
            $organization->activeSubscription()->cancel();
        }

        // Calculate subscription period
        $startsAt = now();
        $endsAt = $request->billing_cycle === 'yearly' 
            ? $startsAt->copy()->addYear()
            : $startsAt->copy()->addMonth();

        // Create new subscription
        $subscription = $organization->subscriptions()->create([
            'plan_id' => $plan->id,
            'status' => 'pending',
            'starts_at' => $startsAt,
            'ends_at' => $endsAt,
            'amount' => $plan->price,
            'payment_method' => $request->payment_method,
            'usage_limits' => [
                'current' => [],
                'reset_at' => $startsAt->toISOString()
            ]
        ]);

        if ($request->payment_method === 'stripe') {
            // Handle Stripe payment
            return $this->handleStripePayment($subscription, $request);
        } else {
            // Handle bank transfer
            return $this->handleBankTransfer($subscription);
        }
    }

    /**
     * Cancel subscription
     */
    public function cancel(Organization $organization): JsonResponse
    {
        $this->authorize('manage', $organization);
        
        $subscription = $organization->activeSubscription();
        
        if (!$subscription) {
            return response()->json([
                'message' => 'Không có gói dịch vụ nào đang hoạt động'
            ], 404);
        }

        $subscription->cancel();

        return response()->json([
            'message' => 'Đã hủy gói dịch vụ thành công'
        ]);
    }

    /**
     * Handle Stripe payment
     */
    private function handleStripePayment(Subscription $subscription, Request $request): JsonResponse
    {
        // TODO: Implement Stripe integration
        // For now, just return payment URL
        
        return response()->json([
            'message' => 'Đã tạo subscription thành công',
            'data' => [
                'subscription_id' => $subscription->id,
                'payment_url' => 'https://checkout.stripe.com/...',
                'status' => 'pending_payment'
            ]
        ]);
    }

    /**
     * Handle bank transfer payment
     */
    private function handleBankTransfer(Subscription $subscription): JsonResponse
    {
        return response()->json([
            'message' => 'Đã tạo subscription thành công. Vui lòng chuyển khoản theo thông tin bên dưới.',
            'data' => [
                'subscription_id' => $subscription->id,
                'amount' => $subscription->amount,
                'bank_info' => [
                    'bank_name' => 'Vietcombank',
                    'account_number' => '**********',
                    'account_name' => 'CONG TY ABC',
                    'transfer_content' => "SUB-{$subscription->id}"
                ],
                'status' => 'pending_payment'
            ]
        ]);
    }

    /**
     * Webhook for payment confirmation
     */
    public function webhook(Request $request): JsonResponse
    {
        // Handle payment webhooks from Stripe or bank
        $paymentMethod = $request->input('payment_method');
        
        if ($paymentMethod === 'stripe') {
            return $this->handleStripeWebhook($request);
        } elseif ($paymentMethod === 'bank_transfer') {
            return $this->handleBankWebhook($request);
        }

        return response()->json(['message' => 'Invalid payment method'], 400);
    }

    /**
     * Handle Stripe webhook
     */
    private function handleStripeWebhook(Request $request): JsonResponse
    {
        // TODO: Implement Stripe webhook handling
        // Verify webhook signature, update subscription status
        
        return response()->json(['message' => 'Webhook processed']);
    }

    /**
     * Handle bank transfer webhook
     */
    private function handleBankWebhook(Request $request): JsonResponse
    {
        $request->validate([
            'subscription_id' => 'required|exists:subscriptions,id',
            'amount' => 'required|numeric',
            'reference' => 'required|string'
        ]);

        $subscription = Subscription::findOrFail($request->subscription_id);
        
        // Verify amount matches
        if ($request->amount != $subscription->amount) {
            return response()->json(['message' => 'Amount mismatch'], 400);
        }

        // Activate subscription
        $subscription->update([
            'status' => 'active',
            'payment_reference' => $request->reference,
            'last_payment_at' => now()
        ]);

        return response()->json(['message' => 'Payment confirmed, subscription activated']);
    }
}
