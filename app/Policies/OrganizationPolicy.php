<?php

namespace App\Policies;

use App\Models\Organization;
use App\Models\User;

class OrganizationPolicy
{
    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Organization $organization): bool
    {
        return $organization->hasUser($user);
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, Organization $organization): bool
    {
        return $user->isOrganizationAdmin($organization);
    }

    public function delete(User $user, Organization $organization): bool
    {
        return $organization->ownedBy($user);
    }

    public function restore(User $user, Organization $organization): bool
    {
        return $organization->ownedBy($user);
    }

    public function forceDelete(User $user, Organization $organization): bool
    {
        return $organization->ownedBy($user);
    }

    public function invite(User $user, Organization $organization): bool
    {
        return $user->isOrganizationAdmin($organization);
    }

    public function updateMember(User $user, Organization $organization): bool
    {
        return $user->isOrganizationAdmin($organization);
    }

    public function removeMember(User $user, Organization $organization): bool
    {
        return $user->isOrganizationAdmin($organization);
    }

    public function transferOwnership(User $user, Organization $organization): bool
    {
        return $organization->ownedBy($user);
    }
}
