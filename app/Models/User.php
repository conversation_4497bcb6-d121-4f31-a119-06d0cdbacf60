<?php

namespace App\Models;

use App\Enums\OrganizationRole;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'current_organization_id',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function ownedOrganizations(): HasMany
    {
        return $this->hasMany(Organization::class);
    }

    /**
     * @return BelongsToMany<Organization>
     */
    public function organizations(): BelongsToMany
    {
        return $this
            ->belongsToMany(Organization::class)
            ->withPivot(['role', 'joined_at'])
            ->withTimestamps();
    }

    /**
     * @return BelongsTo<Organization>
     */
    public function currentOrganization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'current_organization_id');
    }

    /**
     * @return HasMany<OrganizationInvitation>
     */
    public function sentInvitations(): HasMany
    {
        return $this->hasMany(OrganizationInvitation::class, 'invited_by');
    }

    public function switchOrganization(Organization $organization): bool
    {
        if (! $this->belongsToOrganization($organization)) {
            return false;
        }

        $this->update(['current_organization_id' => $organization->id]);

        return true;
    }

    public function belongsToOrganization(Organization $organization): bool
    {
        return $this->organizations()->where('organization_id', $organization->id)->exists();
    }

    public function ownsOrganization(Organization $organization): bool
    {
        return $this->ownedOrganizations()->where('id', $organization->id)->exists();
    }

    public function getOrganizationRole(Organization $organization): ?OrganizationRole
    {
        $pivot = $this->organizations()->where('organization_id', $organization->id)->first()?->pivot;

        return $pivot?->role ? OrganizationRole::from($pivot->role) : null;
    }

    public function hasOrganizationRole(Organization $organization, OrganizationRole $role): bool
    {
        return $this->getOrganizationRole($organization) === $role;
    }

    public function isOrganizationAdmin(Organization $organization): bool
    {
        $role = $this->getOrganizationRole($organization);

        return in_array($role, [OrganizationRole::Owner, OrganizationRole::Admin]);
    }
}
